#!/usr/bin/env python3
"""
Demonstration of the simplified tech_news function.
"""

from webscraptest import tech_news
import json

def main():
    """Demonstrate the tech_news function."""

    print("=== Comprehensive Tech News Scraper Demo ===")
    print("This function scrapes tech news articles from 16 major tech news sources")
    print("and returns only today's articles that match technical keywords.\n")

    # Comprehensive list of tech news URLs to scrape
    urls = [
        # Original sources
        "https://news.ycombinator.com/",
        "https://www.theverge.com/",
        "https://techcrunch.com/category/artificial-intelligence/",
        "https://scitechdaily.com/news/technology/",

        # New comprehensive sources
        "https://devblogs.microsoft.com/engineering-at-microsoft/",
        "https://www.theverge.com/ai-artificial-intelligence",
        "https://arstechnica.com/ai/",
        "https://arstechnica.com/gadgets/",
        "https://www.analyticsinsight.net/news",
        "https://www.innovationnewsnetwork.com/artificial-intelligence/",
        "https://www.innovationnewsnetwork.com/computer-science/",
        "https://www.innovationnewsnetwork.com/cybersecurity/",
        "https://www.innovationnewsnetwork.com/category/quantum-news/",
        "https://techxplore.com/hi-tech-news/",
        "https://openai.com/news/",
        "https://www.linkedin.com/blog/engineering"
    ]

    print(f"Scraping {len(urls)} tech news websites...")
    print("URLs:")
    for url in urls:
        print(f"  - {url}")

    print("\nFiltering criteria:")
    print("  - Articles published today only")
    print("  - Titles containing technical keywords (AI, ML, blockchain, etc.)")
    print("  - Excluding business drama and non-technical content")

    try:
        # Call the tech_news function
        articles = tech_news(urls)

        print(f"\n=== RESULTS ===")
        print(f"Found {len(articles)} relevant tech articles from today:\n")

        if articles:
            # Display results
            for i, article in enumerate(articles, 1):
                print(f"{i}. {article['title']}")
                print(f"   URL: {article['url']}\n")

            # Save results to JSON file
            output_file = 'todays_tech_news.json'
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(articles, f, indent=2, ensure_ascii=False)
            print(f"Results saved to {output_file}")

            # Show JSON format
            print(f"\nJSON format:")
            print(json.dumps(articles, indent=2))

        else:
            print("No articles found matching the criteria.")
            print("This could mean:")
            print("  - No tech articles were published today on these sites")
            print("  - Articles don't match the technical keyword filters")
            print("  - Date extraction failed for the articles")

    except Exception as e:
        print(f"Error occurred: {e}")

if __name__ == "__main__":
    main()
