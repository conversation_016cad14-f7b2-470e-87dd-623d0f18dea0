import requests
from bs4 import BeautifulSoup
import time
import random
import re
import datetime
from urllib.parse import urljoin, urlparse
from dateutil import parser as date_parser

# Keywords for filtering technical content
INCLUDE_KEYWORDS = [
    # AI technologies
    "ai", "artificial intelligence", "machine learning", "deep learning", "neural network",
    # AI models and applications
    "gpt", "llm", "large language model", "chatbot", "generative ai",
    # Technical domains
    "robotics", "automation", "nlp", "natural language processing", "blockchain", "quantum computing",
    # Research and innovation terms
    "innovation", "research", "breakthrough", "algorithm", "model", "framework",
    # Implementation terms
    "launch", "platform", "deployment", "api", "infrastructure", "open source"
]

# Keywords for filtering out non-technical content
EXCLUDE_KEYWORDS = [
    # Business drama
    "lawsuit", "litigation", "scandal", "controversy", "legal battle",
    # Corporate news
    "ceo", "executive", "resign", "layoffs", "fired", "stock price", "valuation",
    # Social media drama
    "twitter", "x", "elon musk", "meta", "zuckerberg",
    # Speculative content
    "rumor", "leak", "allegedly", "sources say", "reportedly",
    # Generic business news
    "funding", "acquisition", "merger", "investor", "quarterly results"
]

def is_relevant_article(article, strict=False):
    """
    Determine if an article is relevant based on keyword matching.

    Args:
        article (dict): Article dictionary containing title and other fields
        strict (bool): If True, requires multiple include keywords to match

    Returns:
        bool: True if the article is relevant, False otherwise
    """
    # Extract the title and convert to lowercase for case-insensitive matching
    title = article.get('title', '').lower()

    # Skip empty titles or placeholder titles
    if not title or title == "no title found" or title == "most popular":
        return False

    # Check for include keywords with word boundary handling
    include_matches = []
    for keyword in INCLUDE_KEYWORDS:
        # Use word boundary pattern to avoid partial matches
        # e.g., "ai" should match "ai" but not "paid" or "main"
        pattern = r'\b' + re.escape(keyword.lower()) + r'\b'
        if re.search(pattern, title):
            include_matches.append(keyword)

    # Check for exclude keywords with word boundary handling
    exclude_match = False
    for keyword in EXCLUDE_KEYWORDS:
        pattern = r'\b' + re.escape(keyword.lower()) + r'\b'
        if re.search(pattern, title):
            exclude_match = True
            break

    # Determine relevance based on matches and strictness
    if strict:
        # Strict mode: require at least 2 include keywords and no exclude keywords
        is_relevant = len(include_matches) >= 2 and not exclude_match
        return is_relevant
    else:
        # Normal mode: require at least 1 include keyword and no exclude keywords
        is_relevant = len(include_matches) >= 1 and not exclude_match
        return is_relevant

def _extract_date(article):
    """
    Extract the publication date from an article element.

    Args:
        article (bs4.element.Tag): The article element

    Returns:
        datetime.datetime or None: The publication date if found, None otherwise
    """
    # Default date selectors
    date_selectors = [
        'time', '.date', '.time', '.datetime', '.published', '.pubdate',
        '[datetime]', '[pubdate]', '.timestamp', '.post-date', '.entry-date',
        'meta[property="article:published_time"]', 'meta[itemprop="datePublished"]'
    ]

    # Try to find date using selectors
    for selector in date_selectors:
        date_elements = article.select(selector)
        if not date_elements and selector.startswith('meta'):
            # Try to find meta tags in the whole document
            date_elements = article.find_all(selector)

        for date_element in date_elements:
            # Check for datetime attribute
            if date_element.has_attr('datetime'):
                try:
                    return date_parser.parse(date_element['datetime'])
                except (ValueError, TypeError):
                    pass

            # Check for content attribute (meta tags)
            if date_element.has_attr('content'):
                try:
                    return date_parser.parse(date_element['content'])
                except (ValueError, TypeError):
                    pass

            # Try to parse the text content
            try:
                date_text = date_element.get_text().strip()
                if date_text:
                    return date_parser.parse(date_text)
            except (ValueError, TypeError):
                pass

    # Try to find date patterns in the article text
    article_text = article.get_text()

    # Common date patterns
    date_patterns = [
        # ISO format: 2023-05-23
        r'\d{4}-\d{2}-\d{2}',
        # Common formats: May 23, 2023 or 23 May 2023
        r'(?:\d{1,2}\s+)?(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\.?\s+\d{1,2}(?:st|nd|rd|th)?,?\s+\d{4}',
        r'(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\.?\s+\d{1,2}(?:st|nd|rd|th)?,?\s+\d{4}',
        # MM/DD/YYYY or DD/MM/YYYY
        r'\d{1,2}/\d{1,2}/\d{4}',
        # Relative time: "2 hours ago", "yesterday", etc.
        r'(\d+)\s+(second|minute|hour|day|week|month|year)s?\s+ago',
        r'yesterday|today'
    ]

    for pattern in date_patterns:
        match = re.search(pattern, article_text)
        if match:
            try:
                date_str = match.group(0)

                # Handle relative time
                if 'ago' in date_str:
                    num, unit = re.findall(r'(\d+)\s+(second|minute|hour|day|week|month|year)', date_str)[0]
                    num = int(num)
                    now = datetime.datetime.now()

                    if unit == 'second':
                        return now - datetime.timedelta(seconds=num)
                    elif unit == 'minute':
                        return now - datetime.timedelta(minutes=num)
                    elif unit == 'hour':
                        return now - datetime.timedelta(hours=num)
                    elif unit == 'day':
                        return now - datetime.timedelta(days=num)
                    elif unit == 'week':
                        return now - datetime.timedelta(weeks=num)
                    elif unit == 'month':
                        # Approximate a month as 30 days
                        return now - datetime.timedelta(days=30*num)
                    elif unit == 'year':
                        # Approximate a year as 365 days
                        return now - datetime.timedelta(days=365*num)
                elif date_str == 'yesterday':
                    return datetime.datetime.now() - datetime.timedelta(days=1)
                elif date_str == 'today':
                    return datetime.datetime.now()
                else:
                    return date_parser.parse(date_str)
            except (ValueError, TypeError, IndexError):
                pass

    # If we couldn't find a date, return None
    return None


def _is_today(article_date):
    """
    Check if an article's publication date is today.

    Args:
        article_date (datetime.datetime or None): The article's publication date

    Returns:
        bool: True if the article is from today, False otherwise
    """
    if article_date is None:
        return False

    # Make sure we're comparing naive datetimes
    if article_date.tzinfo is not None:
        article_date = article_date.replace(tzinfo=None)

    # Same day only
    now = datetime.datetime.now()
    return (article_date.year == now.year and
            article_date.month == now.month and
            article_date.day == now.day)


def _is_recent(article_date):
    """
    Check if an article's publication date is within the last 2 days.

    Args:
        article_date (datetime.datetime or None): The article's publication date

    Returns:
        bool: True if the article is from the last 2 days, False otherwise
    """
    if article_date is None:
        return False

    # Make sure we're comparing naive datetimes
    if article_date.tzinfo is not None:
        article_date = article_date.replace(tzinfo=None)

    # Within the last 2 days
    now = datetime.datetime.now()
    cutoff_date = now - datetime.timedelta(days=2)
    return article_date >= cutoff_date

def tech_news(urls):
    """
    Scrape tech news articles from provided URLs, filtering for today's articles only.

    Args:
        urls (list): List of URLs to scrape

    Returns:
        list: JSON-serializable list of dictionaries with 'title' and 'url' keys
    """
    all_articles = []

    for url in urls:
        try:
            # Add a random delay to avoid overloading the server
            delay = random.uniform(1, 3)
            time.sleep(delay)

            # Fetch the page
            response = requests.get(url, timeout=30, headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            })
            response.raise_for_status()

            # Parse the HTML
            soup = BeautifulSoup(response.content, "html.parser")

            # Website-specific article detection
            article_elements = []

            if 'news.ycombinator.com' in url:
                # Hacker News uses .athing class for articles
                article_elements = soup.select('.athing')
            elif 'techcrunch.com' in url:
                # TechCrunch uses .post class for articles
                article_elements = soup.select('.post')
            elif 'theverge.com' in url:
                # The Verge uses content cards
                article_elements = soup.select('.duet--content-cards--content-card')
            elif 'scitechdaily.com' in url:
                # SciTechDaily uses entry class
                article_elements = soup.select('article.entry, .entry, .post')
            else:
                # Generic approach for other sites
                article_elements = soup.select('article')

            # If no articles found with specific selectors, try generic approach
            if not article_elements:
                article_elements = soup.find_all(['article', 'div', 'section'],
                    class_=lambda c: c and any(x in c for x in ['article', 'post', 'entry', 'item']))

                # If still no articles found, try to find divs with links and headers
                if not article_elements:
                    for div in soup.find_all('div'):
                        if div.find(['h1', 'h2', 'h3', 'h4']) and div.find('a'):
                            article_elements.append(div)

            base_url = urlparse(url)
            base_domain = f"{base_url.scheme}://{base_url.netloc}"

            for article in article_elements:
                try:
                    # Website-specific title extraction
                    title_element = None

                    if 'news.ycombinator.com' in url:
                        # Hacker News specific title extraction
                        title_element = article.select_one('.titleline > a, .title a')
                    elif 'techcrunch.com' in url:
                        # TechCrunch specific - uses h3 a for titles
                        title_element = article.select_one('h3 a, h2 a')
                    elif 'theverge.com' in url:
                        # The Verge specific - titles are in links within the card
                        title_element = article.select_one('a[href*="/"]')
                        # Filter out comment links and other non-article links
                        if title_element and ('#comments' in title_element.get('href', '') or
                                            title_element.get_text().strip() in ['', 'Comments', 'Comment Icon Bubble']):
                            # Look for the main article link
                            all_links = article.select('a[href*="/"]')
                            for link in all_links:
                                if '#comments' not in link.get('href', '') and link.get_text().strip():
                                    title_element = link
                                    break
                    elif 'scitechdaily.com' in url:
                        # SciTechDaily specific
                        title_element = article.select_one('h2.entry-title a, h1.entry-title a, .entry-title a')

                    # Generic fallback
                    if not title_element:
                        title_element = article.select_one('h2 a, h3 a, h1 a, .title a, .headline a')
                    if not title_element:
                        title_element = article.find(['h1', 'h2', 'h3', 'h4'])

                    if not title_element:
                        continue

                    title = title_element.get_text().strip()
                    if not title or title == "No title found":
                        continue

                    # Extract URL with improved logic
                    link = None

                    # Method 1: Try to get URL from the title element if it's a link
                    if title_element.name == 'a' and title_element.has_attr('href'):
                        link = title_element['href']

                    # Method 2: Look for link in title's parent or nearby elements
                    if not link:
                        # Check if title is inside a link
                        parent_link = title_element.find_parent('a')
                        if parent_link and parent_link.has_attr('href'):
                            link = parent_link['href']

                    # Method 3: Website-specific URL extraction
                    if not link:
                        # Hacker News specific
                        if 'news.ycombinator.com' in url:
                            title_link = article.select_one('.titleline > a')
                            if title_link and title_link.has_attr('href'):
                                link = title_link['href']

                        # TechCrunch specific
                        elif 'techcrunch.com' in url:
                            tc_link = article.select_one('h3 a, h2 a')
                            if tc_link and tc_link.has_attr('href'):
                                link = tc_link['href']

                        # The Verge specific
                        elif 'theverge.com' in url:
                            # Find the main article link (not comments)
                            verge_links = article.select('a[href*="/"]')
                            for verge_link in verge_links:
                                if ('#comments' not in verge_link.get('href', '') and
                                    verge_link.get_text().strip() and
                                    verge_link.has_attr('href')):
                                    link = verge_link['href']
                                    break

                    # Method 4: Generic fallback - find any link that contains title text
                    if not link:
                        all_links = article.find_all('a', href=True)
                        for a_tag in all_links:
                            link_text = a_tag.get_text().strip().lower()
                            if title.lower() in link_text or link_text in title.lower():
                                link = a_tag['href']
                                break

                    # Method 5: Last resort - get the first meaningful link
                    if not link:
                        all_links = article.find_all('a', href=True)
                        for a_tag in all_links:
                            href = a_tag['href']
                            # Skip internal/navigation links
                            if href and not href.startswith('#') and not href.startswith('javascript:'):
                                link = href
                                break

                    if not link:
                        continue

                    # Make sure the link is absolute
                    if not link.startswith(('http://', 'https://')):
                        link = urljoin(base_domain, link)

                    # Extract publication date
                    pub_date = _extract_date(article)

                    # Only include articles from today (or recent if no date found)
                    if _is_today(pub_date) or pub_date is None or _is_recent(pub_date):
                        # Check if article is relevant based on title keywords
                        article_dict = {'title': title}
                        if is_relevant_article(article_dict):
                            all_articles.append({
                                'title': title,
                                'url': link
                            })

                except Exception:
                    # Skip articles that cause parsing errors
                    continue

        except Exception:
            # Skip URLs that cause errors
            continue

    # Remove duplicates based on URL
    seen_urls = set()
    unique_articles = []
    for article in all_articles:
        if article['url'] not in seen_urls:
            seen_urls.add(article['url'])
            unique_articles.append(article)

    return unique_articles

